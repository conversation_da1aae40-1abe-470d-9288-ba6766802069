import os
import torch
import torch.nn as nn
import torch.nn.functional as F

# -----------------------------------------------------------------------------
# ConvNeXt Block Implementation
# -----------------------------------------------------------------------------
class ConvNeXtBlock(nn.Module):
    """ConvNeXt Block with depthwise convolution, layer normalization, and inverted bottleneck."""

    def __init__(self, dim, drop_path=0., layer_scale_init_value=1e-6):
        super().__init__()
        self.dwconv = nn.Conv2d(dim, dim, kernel_size=7, padding=3, groups=dim)  # depthwise conv
        self.norm = nn.LayerNorm(dim, eps=1e-6)
        self.pwconv1 = nn.Linear(dim, 4 * dim)  # pointwise/1x1 convs, implemented with linear layers
        self.act = nn.GELU()
        self.pwconv2 = nn.Linear(4 * dim, dim)
        self.gamma = nn.Parameter(layer_scale_init_value * torch.ones((dim)),
                                requires_grad=True) if layer_scale_init_value > 0 else None
        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()

    def forward(self, x):
        input = x
        x = self.dwconv(x)
        x = x.permute(0, 2, 3, 1)  # (N, C, H, W) -> (N, H, W, C)
        x = self.norm(x)
        x = self.pwconv1(x)
        x = self.act(x)
        x = self.pwconv2(x)
        if self.gamma is not None:
            x = self.gamma * x
        x = x.permute(0, 3, 1, 2)  # (N, H, W, C) -> (N, C, H, W)

        x = input + self.drop_path(x)
        return x


class DropPath(nn.Module):
    """Drop paths (Stochastic Depth) per sample (when applied in main path of residual blocks)."""
    def __init__(self, drop_prob=None):
        super(DropPath, self).__init__()
        self.drop_prob = drop_prob

    def forward(self, x):
        if self.drop_prob == 0. or not self.training:
            return x
        keep_prob = 1 - self.drop_prob
        shape = (x.shape[0],) + (1,) * (x.ndim - 1)  # work with diff dim tensors, not just 2D ConvNets
        random_tensor = keep_prob + torch.rand(shape, dtype=x.dtype, device=x.device)
        random_tensor.floor_()  # binarize
        output = x.div(keep_prob) * random_tensor
        return output


# -----------------------------------------------------------------------------
# ConvNeXt Encoder Implementation
# -----------------------------------------------------------------------------
class ConvNeXtEncoder(nn.Module):
    """Lightweight ConvNeXt encoder following ConvNeXt-T architecture."""

    def __init__(self, in_chans=2, depths=[3, 3, 9, 3], dims=[96, 192, 384, 768],
                 drop_path_rate=0., layer_scale_init_value=1e-6):
        super().__init__()

        self.downsample_layers = nn.ModuleList()  # stem and 3 intermediate downsampling conv layers
        stem = nn.Sequential(
            nn.Conv2d(in_chans, dims[0], kernel_size=4, stride=4),
            nn.LayerNorm(dims[0], eps=1e-6, data_format="channels_first")
        )
        self.downsample_layers.append(stem)

        for i in range(3):
            downsample_layer = nn.Sequential(
                nn.LayerNorm(dims[i], eps=1e-6, data_format="channels_first"),
                nn.Conv2d(dims[i], dims[i+1], kernel_size=2, stride=2),
            )
            self.downsample_layers.append(downsample_layer)

        self.stages = nn.ModuleList()  # 4 feature resolution stages, each consisting of multiple residual blocks
        dp_rates = [x.item() for x in torch.linspace(0, drop_path_rate, sum(depths))]
        cur = 0
        for i in range(4):
            stage = nn.Sequential(
                *[ConvNeXtBlock(dim=dims[i], drop_path=dp_rates[cur + j],
                layer_scale_init_value=layer_scale_init_value) for j in range(depths[i])]
            )
            self.stages.append(stage)
            cur += depths[i]

    def forward(self, x):
        features = []
        for i in range(4):
            x = self.downsample_layers[i](x)
            x = self.stages[i](x)
            features.append(x)
        return features


# Custom LayerNorm for ConvNeXt
class LayerNorm(nn.Module):
    """LayerNorm that supports two data formats: channels_last (default) or channels_first."""

    def __init__(self, normalized_shape, eps=1e-6, data_format="channels_last"):
        super().__init__()
        self.weight = nn.Parameter(torch.ones(normalized_shape))
        self.bias = nn.Parameter(torch.zeros(normalized_shape))
        self.eps = eps
        self.data_format = data_format
        if self.data_format not in ["channels_last", "channels_first"]:
            raise NotImplementedError
        self.normalized_shape = (normalized_shape, )

    def forward(self, x):
        if self.data_format == "channels_last":
            return F.layer_norm(x, self.normalized_shape, self.weight, self.bias, self.eps)
        elif self.data_format == "channels_first":
            u = x.mean(1, keepdim=True)
            s = (x - u).pow(2).mean(1, keepdim=True)
            x = (x - u) / torch.sqrt(s + self.eps)
            x = self.weight[:, None, None] * x + self.bias[:, None, None]
            return x


# Patch LayerNorm to use custom implementation
nn.LayerNorm = LayerNorm


# -----------------------------------------------------------------------------
# ConvNeXt-UNet Decoder Implementation
# -----------------------------------------------------------------------------
class ConvNeXtUNetDecoder(nn.Module):
    """ConvNeXt-UNet decoder with skip connections for feature reconstruction."""

    def __init__(self, encoder_dims=[96, 192, 384, 768], decoder_dims=[384, 192, 96, 48]):
        super().__init__()

        # Upsampling layers
        self.upsamples = nn.ModuleList()
        self.decoder_blocks = nn.ModuleList()

        # From bottleneck (768) to decoder_dims[0] (384)
        self.upsamples.append(
            nn.ConvTranspose2d(encoder_dims[3], decoder_dims[0], kernel_size=2, stride=2)
        )
        self.decoder_blocks.append(
            nn.Sequential(
                nn.Conv2d(decoder_dims[0] + encoder_dims[2], decoder_dims[0], kernel_size=3, padding=1),
                nn.LayerNorm(decoder_dims[0], data_format="channels_first"),
                nn.GELU(),
                ConvNeXtBlock(decoder_dims[0])
            )
        )

        # From decoder_dims[0] (384) to decoder_dims[1] (192)
        self.upsamples.append(
            nn.ConvTranspose2d(decoder_dims[0], decoder_dims[1], kernel_size=2, stride=2)
        )
        self.decoder_blocks.append(
            nn.Sequential(
                nn.Conv2d(decoder_dims[1] + encoder_dims[1], decoder_dims[1], kernel_size=3, padding=1),
                nn.LayerNorm(decoder_dims[1], data_format="channels_first"),
                nn.GELU(),
                ConvNeXtBlock(decoder_dims[1])
            )
        )

        # From decoder_dims[1] (192) to decoder_dims[2] (96)
        self.upsamples.append(
            nn.ConvTranspose2d(decoder_dims[1], decoder_dims[2], kernel_size=2, stride=2)
        )
        self.decoder_blocks.append(
            nn.Sequential(
                nn.Conv2d(decoder_dims[2] + encoder_dims[0], decoder_dims[2], kernel_size=3, padding=1),
                nn.LayerNorm(decoder_dims[2], data_format="channels_first"),
                nn.GELU(),
                ConvNeXtBlock(decoder_dims[2])
            )
        )

        # Final upsampling to original resolution
        self.upsamples.append(
            nn.ConvTranspose2d(decoder_dims[2], decoder_dims[3], kernel_size=4, stride=4)
        )
        self.decoder_blocks.append(
            nn.Sequential(
                nn.Conv2d(decoder_dims[3], decoder_dims[3], kernel_size=3, padding=1),
                nn.LayerNorm(decoder_dims[3], data_format="channels_first"),
                nn.GELU()
            )
        )

    def forward(self, encoder_features):
        """
        Args:
            encoder_features: List of features from encoder [feat0, feat1, feat2, feat3]
                             where feat3 is the bottleneck feature
        Returns:
            Decoded feature map at original resolution
        """
        x = encoder_features[3]  # Start from bottleneck

        # Decode with skip connections
        for i in range(4):
            x = self.upsamples[i](x)
            if i < 3:  # Skip connection for first 3 layers
                skip_feat = encoder_features[2-i]  # Reverse order: feat2, feat1, feat0
                x = torch.cat([x, skip_feat], dim=1)
            x = self.decoder_blocks[i](x)

        return x


# -----------------------------------------------------------------------------
# Vector to Skew-Symmetric Matrix Conversion
# -----------------------------------------------------------------------------
def vector_to_skew_symmetric(vec, matrix_dim):
    """Convert vector to skew-symmetric matrix.
    Args:
        vec: (batch_size, D), where D = M*(M-1)/2
        matrix_dim: M
    Returns:
        skew_matrix: (batch_size, M, M) skew-symmetric matrix
    """
    batch_size = vec.shape[0]
    device = vec.device
    skew_matrix = torch.zeros(batch_size, matrix_dim, matrix_dim, device=device, dtype=vec.dtype)

    # Get upper triangular indices
    triu_indices = torch.triu_indices(matrix_dim, matrix_dim, offset=1, device=device)

    # Populate the upper triangle
    skew_matrix[:, triu_indices[0], triu_indices[1]] = vec

    # Create the skew-symmetric matrix: A - A^T
    return skew_matrix - skew_matrix.transpose(-2, -1)


# -----------------------------------------------------------------------------
# Enhanced Scaled Cayley Transform Module
# -----------------------------------------------------------------------------
class ScaledCayleyTransform(nn.Module):
    """Enhanced Scaled Cayley Transform for generating orthogonal matrices with numerical stability."""

    def __init__(self, matrix_dim, num_neg_ones_in_D=0, eps=1e-4, learnable_D=False):
        super().__init__()
        self.matrix_dim = matrix_dim
        self.eps = eps  # OPTIMIZED: Increased from 1e-8 to 1e-4 for better numerical stability
        self.learnable_D = learnable_D

        if learnable_D:
            # Learnable D matrix: parameterize as sigmoid-gated signs
            # This allows the network to learn optimal D configuration
            self.D_logits = nn.Parameter(torch.randn(matrix_dim))
            print(f"[ScaledCayleyTransform] Using learnable D matrix with {matrix_dim} parameters")
        else:
            # Fixed D matrix as a non-trainable buffer (original implementation)
            D = torch.ones(matrix_dim)
            if num_neg_ones_in_D > 0:
                D[:num_neg_ones_in_D] = -1.0
            self.register_buffer('D', torch.diag(D))
            print(f"[ScaledCayleyTransform] Using fixed D matrix with {num_neg_ones_in_D} negative ones")

    def get_D_matrix(self):
        """Get the current D matrix (either fixed or learned)."""
        if self.learnable_D:
            # Convert logits to {-1, +1} via tanh and sign
            D_signs = torch.tanh(self.D_logits)  # Smooth approximation
            # For hard assignment: D_signs = torch.sign(self.D_logits)
            return torch.diag(D_signs)
        else:
            return self.D

    def forward(self, A):
        """Apply Enhanced Scaled Cayley Transform: W = (I - A) @ inv(I + A) @ D

        Enhanced with numerical stability improvements:
        - Regularization for matrix inversion
        - Double precision computation for critical operations
        - Proper handling of complex matrices

        Args:
            A: skew-symmetric matrix (batch_size, M, M)
        Returns:
            W: orthogonal matrix (batch_size, M, M)
        """
        batch_size = A.shape[0]
        device = A.device
        dtype = A.dtype

        # Create identity matrix
        Id = torch.eye(self.matrix_dim, device=device, dtype=dtype).expand(batch_size, -1, -1)

        # OPTIMIZED: Enhanced numerical stability with better regularization
        if dtype == torch.float32:
            A_double = A.double()
            Id_double = Id.double()

            # Compute (I + A) with enhanced regularization for stability
            I_plus_A = Id_double + A_double

            # OPTIMIZED: Stronger regularization with condition-number aware adjustment
            base_reg = self.eps * torch.eye(self.matrix_dim, device=device, dtype=torch.float64)

            # Adaptive regularization based on matrix magnitude
            A_norm = torch.norm(A_double, dim=(-2, -1), keepdim=True)
            adaptive_reg = torch.clamp(A_norm * 1e-3, min=self.eps, max=1e-2)
            reg_term = adaptive_reg.unsqueeze(-1) * torch.eye(self.matrix_dim, device=device, dtype=torch.float64)

            I_plus_A = I_plus_A + reg_term

            # Compute (I - A)
            I_minus_A = Id_double - A_double

            # OPTIMIZED: More robust solving with better fallback
            try:
                # Check condition number before solving
                cond_num = torch.linalg.cond(I_plus_A)
                if torch.any(cond_num > 1e12):
                    # Use SVD-based pseudo-inverse for ill-conditioned matrices
                    Q = torch.matmul(torch.linalg.pinv(I_plus_A, rcond=1e-6), I_minus_A)
                else:
                    Q = torch.linalg.solve(I_plus_A, I_minus_A)
            except RuntimeError:
                # Enhanced fallback with better conditioning
                Q = torch.matmul(torch.linalg.pinv(I_plus_A, rcond=1e-6), I_minus_A)

            # Convert back to original precision
            Q = Q.to(dtype)
        else:
            # OPTIMIZED: Direct computation with enhanced stability for other dtypes
            I_plus_A = Id + A
            I_minus_A = Id - A

            # OPTIMIZED: Enhanced regularization for non-float32 dtypes
            base_reg = self.eps * torch.eye(self.matrix_dim, device=device, dtype=dtype)
            A_norm = torch.norm(A, dim=(-2, -1), keepdim=True)
            adaptive_reg = torch.clamp(A_norm * 1e-3, min=self.eps, max=1e-2)
            reg_term = adaptive_reg.unsqueeze(-1) * torch.eye(self.matrix_dim, device=device, dtype=dtype)

            I_plus_A = I_plus_A + reg_term

            try:
                # Check condition number before solving
                cond_num = torch.linalg.cond(I_plus_A)
                if torch.any(cond_num > 1e10):
                    Q = torch.matmul(torch.linalg.pinv(I_plus_A, rcond=1e-6), I_minus_A)
                else:
                    Q = torch.linalg.solve(I_plus_A, I_minus_A)
            except RuntimeError:
                Q = torch.matmul(torch.linalg.pinv(I_plus_A, rcond=1e-6), I_minus_A)

        # Apply the scaling matrix D (either fixed or learned)
        D_matrix = self.get_D_matrix().to(dtype)
        W = torch.matmul(Q, D_matrix)

        return W

    def check_orthogonality(self, W):
        """Check orthogonality of the output matrix for debugging."""
        WtW = torch.matmul(W.transpose(-2, -1), W)
        I = torch.eye(W.size(-1), device=W.device, dtype=W.dtype)
        error = torch.mean(torch.abs(WtW - I) ** 2)
        return error.item()


# -----------------------------------------------------------------------------
# Enhanced Unitary Matrix Generator
# -----------------------------------------------------------------------------
class UnitaryGenerator(nn.Module):
    """Enhanced module that converts parameter vectors to unitary matrices with complex support."""

    def __init__(self, matrix_dim, rank, num_neg_ones_in_D=0, complex_matrices=True,
                 learnable_D=False):
        super().__init__()
        self.matrix_dim = matrix_dim
        self.rank = rank
        self.complex_matrices = complex_matrices
        self.learnable_D = learnable_D

        if complex_matrices:
            # For complex matrices, we need parameters for both real and imaginary parts
            # of the skew-Hermitian matrix: A^H = -A
            # This requires matrix_dim^2 real parameters for a full skew-Hermitian matrix
            self.param_dim = matrix_dim * matrix_dim
        else:
            # For real matrices, skew-symmetric: A^T = -A
            self.param_dim = matrix_dim * (matrix_dim - 1) // 2

        # Enhanced Cayley transform with configurable D matrix
        self.cayley_transform = ScaledCayleyTransform(
            matrix_dim,
            num_neg_ones_in_D=num_neg_ones_in_D,
            learnable_D=learnable_D
        )

    def forward(self, param_vec):
        """Convert parameter vector to unitary matrix.
        Args:
            param_vec: (batch_size, param_dim) parameter vector
        Returns:
            unitary_mat: (batch_size, matrix_dim, rank, 2) truncated unitary matrix
        """
        batch_size = param_vec.shape[0]

        if self.complex_matrices:
            # Convert vector to skew-Hermitian matrix for complex case
            A = self._vector_to_skew_hermitian(param_vec, self.matrix_dim)
        else:
            # Convert vector to skew-symmetric matrix for real case
            A = vector_to_skew_symmetric(param_vec, self.matrix_dim)

        # Apply Scaled Cayley Transform to get unitary/orthogonal matrix
        W = self.cayley_transform(A)  # (batch_size, matrix_dim, matrix_dim)

        # Truncate to get the first R columns
        W_truncated = W[:, :, :self.rank]  # (batch_size, matrix_dim, rank)

        if self.complex_matrices and torch.is_complex(W_truncated):
            # Convert complex tensor to real-imaginary format
            W_ri = torch.stack([W_truncated.real, W_truncated.imag], dim=-1)
        else:
            # For real matrices, imaginary part is zero
            W_ri = torch.stack([W_truncated, torch.zeros_like(W_truncated)], dim=-1)

        return W_ri

    def _vector_to_skew_hermitian(self, vec, matrix_dim):
        """Convert vector to skew-Hermitian matrix A where A^H = -A.

        Args:
            vec: (batch_size, matrix_dim^2) parameter vector
            matrix_dim: dimension of the output matrix
        Returns:
            skew_hermitian: (batch_size, matrix_dim, matrix_dim) skew-Hermitian matrix
        """
        batch_size = vec.shape[0]
        device = vec.device
        dtype = torch.complex64 if vec.dtype == torch.float32 else torch.complex128

        # Reshape vector to matrix form
        vec_reshaped = vec.view(batch_size, matrix_dim, matrix_dim)

        # Create skew-Hermitian matrix
        # Diagonal elements are purely imaginary
        diag_imag = vec_reshaped[:, range(matrix_dim), range(matrix_dim)]

        # Off-diagonal elements: upper triangle defines the matrix
        triu_indices = torch.triu_indices(matrix_dim, matrix_dim, offset=1, device=device)

        # Create complex matrix
        A = torch.zeros(batch_size, matrix_dim, matrix_dim, dtype=dtype, device=device)

        # Set diagonal to purely imaginary
        A[:, range(matrix_dim), range(matrix_dim)] = 1j * diag_imag

        # Set upper triangle
        upper_real = vec_reshaped[:, triu_indices[0], triu_indices[1]]
        upper_imag = torch.zeros_like(upper_real)  # Can be extended to include imaginary parts
        A[:, triu_indices[0], triu_indices[1]] = upper_real + 1j * upper_imag

        # Set lower triangle as conjugate transpose of upper triangle
        A[:, triu_indices[1], triu_indices[0]] = -torch.conj(A[:, triu_indices[0], triu_indices[1]])

        return A


# -----------------------------------------------------------------------------
# Enhanced SVDNet with ConvNeXt-UNet Architecture
# -----------------------------------------------------------------------------
class SVDNet(nn.Module):
    """Enhanced SVDNet with ConvNeXt-UNet backbone and geometric-aware design.

    Architecture follows the optimization plan:
    - Lightweight ConvNeXt-UNet encoder-decoder with skip connections
    - Multi-task prediction heads for U, V, and S
    - Enhanced Scaled Cayley Transform for hard orthogonality constraints
    - Support for complex matrices and numerical stability
    """

    def __init__(self, dim: int = 64, rank: int = 32, weight_path: str = "svdnet.pth",
                 num_neg_ones_in_D: int = 0, learnable_D: bool = False):
        super().__init__()
        self.dim = dim  # Antenna dimension (M == N)
        self.rank = rank

        # --------------------------- ConvNeXt-UNet Backbone ----------------------------
        # LIGHTWEIGHT: Reduced ConvNeXt dimensions for efficiency
        encoder_dims = [64, 128, 256, 384]  # Reduced from [96, 192, 384, 768]
        decoder_dims = [256, 128, 64, 128]  # Proportionally reduced

        self.encoder = ConvNeXtEncoder(
            in_chans=2,
            depths=[2, 2, 4, 2],  # LIGHTWEIGHT: Reduced depths from [3, 3, 9, 3]
            dims=encoder_dims,
            drop_path_rate=0.05,  # Reduced dropout path rate
            layer_scale_init_value=1e-6
        )

        # ConvNeXt-UNet decoder with skip connections
        self.decoder = ConvNeXtUNetDecoder(
            encoder_dims=encoder_dims,
            decoder_dims=decoder_dims
        )

        # ------------------------ Multi-task Prediction Heads ----------------------------
        # OPTIMIZED: Shared feature dimension from decoder (increased from 48 to 128)
        shared_dim = decoder_dims[3]  # 128

        # LIGHTWEIGHT: Simplified U prediction head (reduced from 3 layers to 2)
        self.head_U = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),  # Global average pooling
            nn.Flatten(),
            nn.Linear(shared_dim, 512),  # Single intermediate layer
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(512, dim * dim),   # Direct output to parameters
        )

        # LIGHTWEIGHT: Simplified V prediction head (reduced from 3 layers to 2)
        self.head_V = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),  # Global average pooling
            nn.Flatten(),
            nn.Linear(shared_dim, 512),  # Single intermediate layer
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(512, dim * dim),   # Direct output to parameters
        )

        # LIGHTWEIGHT: Simplified S prediction head (reduced complexity)
        self.head_S = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),  # Global average pooling
            nn.Flatten(),
            nn.Linear(shared_dim, 256),  # Reduced intermediate dimension
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(256, rank),        # Direct output to singular values
            nn.Softplus()  # Ensure non-negative singular values
        )

        # ----------------------- Enhanced Unitary Generators ------------------------
        # Use enhanced generators with complex matrix support and configurable D matrix
        self.generator_U = UnitaryGenerator(
            dim, rank,
            num_neg_ones_in_D=num_neg_ones_in_D,
            complex_matrices=True,
            learnable_D=learnable_D
        )
        self.generator_V = UnitaryGenerator(
            dim, rank,
            num_neg_ones_in_D=num_neg_ones_in_D,
            complex_matrices=True,
            learnable_D=learnable_D
        )

        # ------------------------ Weight loading ----------------------------
        if os.path.isfile(weight_path):
            state = torch.load(weight_path, map_location="cpu")
            try:
                self.load_state_dict(state, strict=False)
                print(f"[Enhanced SVDNet] Loaded weights from {weight_path} (strict=False)")
            except RuntimeError as e:
                print(f"[Enhanced SVDNet] Weight loading failed: {e}. Proceeding with random init.")

    # ---------------------------------------------------------------------
    def forward(self, x: torch.Tensor):
        """Enhanced forward pass with ConvNeXt-UNet backbone.
        Args:
            x: complex channel, shape [M, N, 2] or [B, M, N, 2]
        Returns:
            U: left unitary matrix [B, M, R, 2] or [M, R, 2]
            S: singular values [B, R] or [R]
            V: right unitary matrix [B, N, R, 2] or [N, R, 2]
        """
        if x.ndim == 3:
            x = x.unsqueeze(0)
        if x.shape[-1] != 2:
            raise ValueError("Input last dim must be 2 (real/imag)")
        B = x.size(0)

        # Reorder to NCHW format for ConvNeXt processing
        feat = x.permute(0, 3, 1, 2)  # [B, 2, M, N]

        # ConvNeXt encoder: extract hierarchical features
        encoder_features = self.encoder(feat)  # List of features at different scales

        # ConvNeXt-UNet decoder: reconstruct features with skip connections
        decoded_feat = self.decoder(encoder_features)  # [B, 48, M, N]

        # Multi-task prediction heads
        # U head: predict parameters for unitary matrix U
        U_params = self.head_U(decoded_feat)  # [B, dim*dim]

        # V head: predict parameters for unitary matrix V
        V_params = self.head_V(decoded_feat)  # [B, dim*dim]

        # S head: predict singular values
        S = self.head_S(decoded_feat)  # [B, rank]

        # Generate unitary matrices using Enhanced Scaled Cayley Transform
        U = self.generator_U(U_params)  # [B, dim, rank, 2]
        V = self.generator_V(V_params)  # [B, dim, rank, 2]

        # Remove batch dim if B==1 (to match demo expectations)
        if B == 1:
            U = U.squeeze(0)
            V = V.squeeze(0)
            S = S.squeeze(0)

        return U, S, V

    # ------------------------------------------------------------------
    @staticmethod
    def _to_complex(mat: torch.Tensor) -> torch.Tensor:
        """Convert real-imag stacked tensor [..., 2] → complex."""
        return torch.complex(mat[..., 0], mat[..., 1])

    @staticmethod
    def _to_ri(mat: torch.Tensor) -> torch.Tensor:
        """Convert complex tensor → real-imag stacked."""
        return torch.stack((mat.real, mat.imag), dim=-1)

    def get_model_complexity(self):
        """Calculate model complexity in terms of parameters and FLOPs."""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)

        return {
            'total_params': total_params,
            'trainable_params': trainable_params,
            'model_size_mb': total_params * 4 / (1024 * 1024)  # Assuming float32
        }